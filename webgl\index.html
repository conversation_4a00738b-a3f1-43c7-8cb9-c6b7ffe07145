<!DOCTYPE html>
<html lang="en-us">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Unity WebGL Player | BaiYunGroup</title>
    <link rel="shortcut icon" href="TemplateData/favicon.ico">
    <link rel="stylesheet" href="TemplateData/style.css">
    <link rel="manifest" href="manifest.webmanifest">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
      /**
       * 桂林智源 Unity WebGL 界面切换容器样式
       * 实现Unity 3D界面和电气拓扑图之间的切换功能
       */

      /* 主包装容器 */
      #main-wrapper {
        position: fixed;
        width: 100%;
        height: 100%;
        background: #0a0f1c;
        overflow: hidden;
      }

      /* 切换控制区域 */
      .switch-controls {
        position: absolute;
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
        display: flex;
        align-items: center;
        gap: 15px;
        z-index: 1000;
        background: rgba(26, 35, 50, 0.9);
        padding: 10px 20px;
        border-radius: 25px;
        border: 2px solid #00d4ff;
        box-shadow: 0 4px 20px rgba(0, 212, 255, 0.3);
        backdrop-filter: blur(10px);
      }

      /* 导航按钮 */
      .nav-btn {
        padding: 8px 16px;
        background: linear-gradient(135deg, #1a2332, #2a3441);
        border: 1px solid #00d4ff;
        border-radius: 20px;
        color: #00d4ff;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 8px;
        font-family: 'Microsoft YaHei', sans-serif;
        min-width: 120px;
        justify-content: center;
      }

      .nav-btn:hover {
        background: linear-gradient(135deg, #00d4ff, #0099cc);
        color: #0a0f1c;
        box-shadow: 0 2px 15px rgba(0, 212, 255, 0.5);
        transform: translateY(-2px);
      }

      .nav-btn.active {
        background: linear-gradient(135deg, #00d4ff, #00ff88);
        color: #0a0f1c;
        box-shadow: 0 2px 15px rgba(0, 212, 255, 0.6);
      }

      .nav-btn i {
        font-size: 16px;
      }

      /* 页面指示器 */
      .page-indicator {
        display: flex;
        align-items: center;
        gap: 8px;
        color: #b8c5d6;
        font-size: 14px;
        font-family: 'Microsoft YaHei', sans-serif;
      }

      .indicator-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #3a4a5c;
        transition: all 0.3s ease;
      }

      .indicator-dot.active {
        background: #00ff88;
        box-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
      }

      /* Unity容器样式调整 */
      #unity-container {
        position: absolute;
        width: 100%;
        height: 100%;
        transition: opacity 0.5s ease;
      }

      /* 电气拓扑iframe容器 */
      #topology-container {
        position: absolute;
        width: 100%;
        height: 100%;
        opacity: 0;
        visibility: hidden;
        transition: all 0.5s ease;
      }

      #topology-iframe {
        width: 100%;
        height: 100%;
        border: none;
        background: #0a0f1c;
      }

      /* 显示状态控制 */
      .show-unity #unity-container {
        opacity: 1;
        visibility: visible;
      }

      .show-unity #topology-container {
        opacity: 0;
        visibility: hidden;
      }

      .show-topology #unity-container {
        opacity: 0;
        visibility: hidden;
      }

      .show-topology #topology-container {
        opacity: 1;
        visibility: visible;
      }

      /* 加载动画 */
      .loading-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(10, 15, 28, 0.9);
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        gap: 20px;
        z-index: 999;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
      }

      .loading-overlay.show {
        opacity: 1;
        visibility: visible;
      }

      .loading-spinner {
        width: 50px;
        height: 50px;
        border: 3px solid #3a4a5c;
        border-top: 3px solid #00d4ff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      .loading-text {
        color: #b8c5d6;
        font-size: 16px;
        font-family: 'Microsoft YaHei', sans-serif;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }

      /* 响应式设计 */
      @media (max-width: 1366px) {
        .switch-controls {
          top: 15px;
          padding: 8px 16px;
        }

        .nav-btn {
          padding: 6px 12px;
          font-size: 13px;
          min-width: 100px;
        }
      }
    </style>
  </head>
  <body>
    <!-- 主包装容器 -->
    <div id="main-wrapper" class="show-unity">
      <!-- 切换控制区域 -->
      <div class="switch-controls">
        <button class="nav-btn active" id="unity-btn" onclick="switchToUnity()">
          <i class="fas fa-cube"></i>
          <span>Unity 3D</span>
        </button>

        <div class="page-indicator">
          <div class="indicator-dot active" id="unity-dot"></div>
          <div class="indicator-dot" id="topology-dot"></div>
        </div>

        <button class="nav-btn" id="topology-btn" onclick="switchToTopology()">
          <i class="fas fa-bolt"></i>
          <span>电气拓扑</span>
        </button>
      </div>

      <!-- Unity容器 -->
      <div id="unity-container">
        <canvas id="unity-canvas" width=3840 height=2160 tabindex="-1"></canvas>
        <div id="unity-loading-bar">
          <div id="unity-logo"></div>
          <div id="unity-progress-bar-empty">
            <div id="unity-progress-bar-full"></div>
          </div>
        </div>
        <div id="unity-warning"> </div>
      </div>

      <!-- 电气拓扑容器 -->
      <div id="topology-container">
        <iframe id="topology-iframe" src=""></iframe>
      </div>

      <!-- 加载动画覆盖层 -->
      <div class="loading-overlay" id="loading-overlay">
        <div class="loading-spinner"></div>
        <div class="loading-text">正在加载页面...</div>
      </div>
    </div>
    <script>
      /**
       * 桂林智源 Unity WebGL 界面切换功能
       * 实现Unity 3D界面和电气拓扑图之间的无缝切换
       */

      // 全局变量
      let currentView = 'unity';
      let topologyLoaded = false;

      window.addEventListener("load", function () {
        if ("serviceWorker" in navigator) {
          navigator.serviceWorker.register("ServiceWorker.js");
        }

        // 初始化界面切换功能
        initViewSwitcher();
      });

      /**
       * 初始化界面切换功能
       */
      function initViewSwitcher() {
        console.log('初始化界面切换功能...');

        // 预加载电气拓扑页面（但不显示）
        preloadTopologyPage();

        // 设置键盘快捷键
        document.addEventListener('keydown', function(e) {
          if (e.ctrlKey && e.key === '1') {
            e.preventDefault();
            switchToUnity();
          } else if (e.ctrlKey && e.key === '2') {
            e.preventDefault();
            switchToTopology();
          }
        });

        console.log('界面切换功能初始化完成');
      }

      /**
       * 预加载电气拓扑页面
       */
      function preloadTopologyPage() {
        const iframe = document.getElementById('topology-iframe');
        if (!topologyLoaded) {
          // iframe.src = '电气拓扑.html';
          iframe.src = 'https://mqtt.qizhiyun.cc/scada/topo/fullscreen?guid=6f2b118e-1b27-4ebd-b1db-bcd08ce10bbf&type=3&date=Mon%20Jul%2014%202025%2009%3A40%3A23%20GMT%200800%20%28%E4%B8%AD%E5%9B%BD%E6%A0%87%E5%87%86%E6%97%B6%E9%97%B4%29';

          iframe.onload = function() {
            topologyLoaded = true;
            console.log('电气拓扑页面预加载完成');
          };

          iframe.onerror = function() {
            console.error('电气拓扑页面加载失败');
          };
        }
      }

      /**
       * 切换到Unity 3D界面
       */
      function switchToUnity() {
        if (currentView === 'unity') return;

        console.log('切换到Unity 3D界面');
        showLoadingOverlay('正在切换到Unity 3D界面...');

        setTimeout(() => {
          const wrapper = document.getElementById('main-wrapper');
          wrapper.className = 'show-unity';

          // 更新按钮状态
          updateButtonStates('unity');

          currentView = 'unity';
          hideLoadingOverlay();
        }, 300);
      }

      /**
       * 切换到电气拓扑页面
       */
      function switchToTopology() {
        if (currentView === 'topology') return;

        console.log('切换到电气拓扑页面');
        showLoadingOverlay('正在切换到电气拓扑页面...');

        // 确保拓扑页面已加载
        if (!topologyLoaded) {
          preloadTopologyPage();
        }

        setTimeout(() => {
          const wrapper = document.getElementById('main-wrapper');
          wrapper.className = 'show-topology';

          // 更新按钮状态
          updateButtonStates('topology');

          currentView = 'topology';
          hideLoadingOverlay();
        }, 300);
      }

      /**
       * 更新按钮状态
       * @param {string} activeView - 当前激活的视图 ('unity' 或 'topology')
       */
      function updateButtonStates(activeView) {
        const unityBtn = document.getElementById('unity-btn');
        const topologyBtn = document.getElementById('topology-btn');
        const unityDot = document.getElementById('unity-dot');
        const topologyDot = document.getElementById('topology-dot');

        // 重置所有按钮状态
        unityBtn.classList.remove('active');
        topologyBtn.classList.remove('active');
        unityDot.classList.remove('active');
        topologyDot.classList.remove('active');

        // 设置激活状态
        if (activeView === 'unity') {
          unityBtn.classList.add('active');
          unityDot.classList.add('active');
        } else if (activeView === 'topology') {
          topologyBtn.classList.add('active');
          topologyDot.classList.add('active');
        }
      }

      /**
       * 显示加载覆盖层
       * @param {string} text - 加载文本
       */
      function showLoadingOverlay(text = '正在加载...') {
        const overlay = document.getElementById('loading-overlay');
        const loadingText = overlay.querySelector('.loading-text');
        loadingText.textContent = text;
        overlay.classList.add('show');
      }

      /**
       * 隐藏加载覆盖层
       */
      function hideLoadingOverlay() {
        const overlay = document.getElementById('loading-overlay');
        overlay.classList.remove('show');
      }

      // Unity WebGL 初始化代码
      var container = document.querySelector("#unity-container");
      var canvas = document.querySelector("#unity-canvas");
      var loadingBar = document.querySelector("#unity-loading-bar");
      var progressBarFull = document.querySelector("#unity-progress-bar-full");
      var warningBanner = document.querySelector("#unity-warning");

      // Shows a temporary message banner/ribbon for a few seconds, or
      // a permanent error message on top of the canvas if type=='error'.
      // If type=='warning', a yellow highlight color is used.
      // Modify or remove this function to customize the visually presented
      // way that non-critical warnings and error messages are presented to the
      // user.
      function unityShowBanner(msg, type) {
        function updateBannerVisibility() {
          warningBanner.style.display = warningBanner.children.length ? 'block' : 'none';
        }
        var div = document.createElement('div');
        div.innerHTML = msg;
        warningBanner.appendChild(div);
        if (type == 'error') div.style = 'background: red; padding: 10px;';
        else {
          if (type == 'warning') div.style = 'background: yellow; padding: 10px;';
          setTimeout(function() {
            warningBanner.removeChild(div);
            updateBannerVisibility();
          }, 5000);
        }
        updateBannerVisibility();
      }

      var buildUrl = "Build";
      var loaderUrl = buildUrl + "/webgl.loader.js";
      var config = {
        dataUrl: buildUrl + "/webgl.data",
        frameworkUrl: buildUrl + "/webgl.framework.js",
        codeUrl: buildUrl + "/webgl.wasm",
        streamingAssetsUrl: "StreamingAssets",
        companyName: "DefaultCompany",
        productName: "BaiYunGroup",
        productVersion: "0.1",
        showBanner: unityShowBanner,
      };

      // By default Unity keeps WebGL canvas render target size matched with
      // the DOM size of the canvas element (scaled by window.devicePixelRatio)
      // Set this to false if you want to decouple this synchronization from
      // happening inside the engine, and you would instead like to size up
      // the canvas DOM size and WebGL render target sizes yourself.
      // config.matchWebGLToCanvasSize = false;

      if (/iPhone|iPad|iPod|Android/i.test(navigator.userAgent)) {
        // Mobile device style: fill the whole browser client area with the game canvas:
        var meta = document.createElement('meta');
        meta.name = 'viewport';
        meta.content = 'width=device-width, height=device-height, initial-scale=1.0, user-scalable=no, shrink-to-fit=yes';
        document.getElementsByTagName('head')[0].appendChild(meta);
      }

      loadingBar.style.display = "block";

      var script = document.createElement("script");
      script.src = loaderUrl;
      script.onload = () => {
        createUnityInstance(canvas, config, (progress) => {
          progressBarFull.style.width = 100 * progress + "%";
        }).then((unityInstance) => {
          loadingBar.style.display = "none";
        }).catch((message) => {
          alert(message);
        });
      };
      document.body.appendChild(script);
    </script>
  </body>
</html>
